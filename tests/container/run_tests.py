#!/usr/bin/env python3
"""
Test runner script for container tests with allure and coverage reporting.

This script provides a convenient way to run tests with proper configuration
for coverage reporting and allure test reporting.

Usage:
    python run_tests.py [options]
    
Options:
    --unit          Run only unit tests
    --integration   Run only integration tests
    --coverage      Generate coverage report
    --allure        Generate allure report
    --parallel      Run tests in parallel
    --verbose       Verbose output
    --help          Show this help message

Examples:
    python run_tests.py --unit --coverage
    python run_tests.py --allure --parallel
    python run_tests.py --coverage --allure --verbose
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description or ' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description or 'Command'} completed successfully")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed with exit code {e.returncode}")
        return e
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        print("Make sure all required dependencies are installed.")
        return None


def install_dependencies():
    """Install test dependencies."""
    requirements_file = Path(__file__).parent / "requirements-test.txt"
    if requirements_file.exists():
        print("Installing test dependencies...")
        run_command([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], "Installing test dependencies")
    else:
        print("⚠️  requirements-test.txt not found, skipping dependency installation")


def run_tests(args):
    """Run tests with specified options."""
    # Base pytest command
    cmd = [sys.executable, "-m", "pytest"]
    
    # Add test file
    test_file = Path(__file__).parent / "test_containers_comprehensive.py"
    cmd.append(str(test_file))
    
    # Add markers based on arguments
    if args.unit:
        cmd.extend(["-m", "unit"])
    elif args.integration:
        cmd.extend(["-m", "integration"])
    
    # Add coverage options
    if args.coverage:
        cmd.extend([
            "--cov=dags.data_pipeline.containers",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml",
            "--cov-fail-under=80"
        ])
    
    # Add allure options
    if args.allure:
        cmd.extend(["--alluredir=allure-results"])
    
    # Add parallel execution
    if args.parallel:
        cmd.extend(["-n", "auto"])
    
    # Add verbosity
    if args.verbose:
        cmd.extend(["-v", "-s"])
    else:
        cmd.append("-v")
    
    # Additional options
    cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--strict-config"
    ])
    
    # Run tests
    result = run_command(cmd, "Running tests")
    
    return result.returncode if hasattr(result, 'returncode') else 1


def generate_allure_report():
    """Generate allure HTML report."""
    allure_results = Path(__file__).parent / "allure-results"
    allure_report = Path(__file__).parent / "allure-report"
    
    if allure_results.exists():
        print("\nGenerating Allure HTML report...")
        result = run_command([
            "allure", "generate", str(allure_results), "-o", str(allure_report), "--clean"
        ], "Generating Allure report")
        
        if result and result.returncode == 0:
            print(f"📊 Allure report generated at: {allure_report}")
            print(f"Open {allure_report / 'index.html'} in your browser to view the report")
        else:
            print("⚠️  Failed to generate Allure report. Make sure 'allure' command is available.")
    else:
        print("⚠️  No allure-results directory found. Run tests with --allure first.")


def generate_coverage_report():
    """Generate coverage HTML report."""
    htmlcov_dir = Path(__file__).parent / "htmlcov"
    
    if htmlcov_dir.exists():
        print(f"\n📊 Coverage report available at: {htmlcov_dir / 'index.html'}")
    else:
        print("⚠️  No coverage report found. Run tests with --coverage first.")


def clean_reports():
    """Clean up old report directories."""
    dirs_to_clean = [
        "allure-results",
        "allure-report", 
        "htmlcov",
        ".coverage",
        "coverage.xml",
        ".pytest_cache"
    ]
    
    for dir_name in dirs_to_clean:
        dir_path = Path(__file__).parent / dir_name
        if dir_path.exists():
            if dir_path.is_file():
                dir_path.unlink()
                print(f"🧹 Removed file: {dir_name}")
            else:
                import shutil
                shutil.rmtree(dir_path)
                print(f"🧹 Removed directory: {dir_name}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Run container tests with coverage and allure reporting",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--allure", action="store_true", help="Generate allure report")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--clean", action="store_true", help="Clean old reports before running")
    parser.add_argument("--report-only", action="store_true", help="Only generate reports (don't run tests)")
    
    args = parser.parse_args()
    
    # Change to test directory
    os.chdir(Path(__file__).parent)
    
    print("🧪 Container Test Runner")
    print("=" * 60)
    
    # Install dependencies if requested
    if args.install_deps:
        install_dependencies()
        return 0
    
    # Clean old reports if requested
    if args.clean:
        clean_reports()
    
    # Generate reports only if requested
    if args.report_only:
        if args.allure:
            generate_allure_report()
        if args.coverage:
            generate_coverage_report()
        return 0
    
    # Run tests
    exit_code = run_tests(args)
    
    # Generate reports
    if args.allure:
        generate_allure_report()
    
    if args.coverage:
        generate_coverage_report()
    
    # Summary
    print("\n" + "=" * 60)
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    print("\nGenerated reports:")
    if args.coverage:
        print("  📊 Coverage: htmlcov/index.html")
    if args.allure:
        print("  📊 Allure: allure-report/index.html")
    
    print("=" * 60)
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())

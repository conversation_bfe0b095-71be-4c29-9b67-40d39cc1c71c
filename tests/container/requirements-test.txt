# Testing dependencies for container tests

# Core testing framework
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0

# Coverage reporting
pytest-cov>=4.0.0
coverage[toml]>=7.0.0

# Allure reporting
allure-pytest>=2.12.0
allure-python-commons>=2.12.0

# Additional testing utilities
pytest-xdist>=3.0.0  # Parallel test execution
pytest-timeout>=2.1.0  # Test timeouts
pytest-benchmark>=4.0.0  # Performance benchmarking
pytest-html>=3.1.0  # HTML test reports

# Mocking and fixtures
responses>=0.23.0  # HTTP mocking
freezegun>=1.2.0  # Time mocking
factory-boy>=3.2.0  # Test data factories

# Database testing
pytest-postgresql>=4.1.0  # PostgreSQL testing
sqlalchemy-utils>=0.40.0  # Database utilities

# Async testing utilities
aioresponses>=0.7.0  # Async HTTP mocking
pytest-aiohttp>=1.0.0  # aiohttp testing

# Code quality
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# coding=utf-8
import asyncio
import atexit
import base64
import json
import logging.config
import os
import signal
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from typing import (
    Dict, Any, Optional, AsyncContextManager, ContextManager,
    Self, AsyncGenerator, Generator
)

import aiohttp
import pandas as pd
from dependency_injector import containers, providers
from dependency_injector.wiring import inject, Provide
from pykeepass import PyKeePass

from sqlalchemy import create_engine, literal, case, cast, func, TEXT, select, inspect
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.sql.coercions import schema
from sqlalchemy_utils import LtreeType
import weakref


try:
    from dbmodels.issue import Issue
    from dbmodels.user import User
except ModuleNotFoundError:
    from .dbmodels.issue import Issue
    from .dbmodels.user import User

logger = logging.getLogger(__name__)

@dataclass
class EntryDetails:
    username: str
    password: str
    url: str | None = None
    custom_properties: Dict[str, Any] = field(default_factory=dict)

    def __repr__(self) -> str:
        return (f"EntryDetails(username={self.username!r}, password='****', "
                f"url={self.url!r}, custom_properties={self.custom_properties!r})")

    def __str__(self) -> str:
        return (f"EntryDetails:\n"
                f"  Username: {self.username}\n"
                f"  Password: ****\n"
                f"  URL: {self.url}\n"
                f"  Custom Properties: {self.custom_properties}")


class EntryDetailsBuilder:
    def __init__(self):
        self._entry_details = EntryDetails(username='', password='')

    def set_username(self, username: str) -> 'EntryDetailsBuilder':
        self._entry_details.username = username
        return self

    def set_password(self, password: str) -> 'EntryDetailsBuilder':
        self._entry_details.password = password
        return self

    def set_url(self, url: Optional[str]) -> 'EntryDetailsBuilder':
        self._entry_details.url = url
        return self

    def add_custom_property(self, key: str, value: Any) -> 'EntryDetailsBuilder':
        self._entry_details.custom_properties[key] = value
        return self

    def build(self) -> EntryDetails:
        return self._entry_details

class PostgresSessionManager:
    # Class-level registry to track all instances for cleanup
    _instances = weakref.WeakSet()
    def __init__(self, entry: EntryDetails, schema: str, rw: bool = True):
        self.schema = schema
        self.entry = entry
        self.rw = rw
        self.engine_async = None  # Initialize with None
        self.engine = None  # Initialize with None
        self._closed = False

        # Register this instance for cleanup
        PostgresSessionManager._instances.add(self)

        self._create_engine_async()
        self._create_engine()

    def _create_engine_async(self):
        self.DATABASE_URL_ASYNC = URL.create(
            drivername="postgresql+asyncpg",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties[
                "DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )
        self.engine_async = create_async_engine(
            self.DATABASE_URL_ASYNC,
            echo=False,
            pool_size=10,  # Adjust pool size as needed
            max_overflow=20,  # Adjust max overflow as needed
            pool_timeout=30,  # Timeout in seconds for acquiring a connection from the pool
            pool_recycle=3600,  # Add connection recycling
            pool_pre_ping=True,  # Add connection health checks
            echo_pool=False,
            # Add connection arguments for better handling
            connect_args={
                "server_settings": {
                    "application_name": f"airflow_app_{self.schema}",
                }
            }
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    def _create_engine(self):

        self.DATABASE_URL = URL.create(
            drivername="postgresql+psycopg2",
            username=self.entry.username,
            password=self.entry.password,
            host=str(self.entry.custom_properties["DB_SERVER_NAME"]),
            port=self.entry.custom_properties["DB_SERVER_RW_PORT"] if self.rw else self.entry.custom_properties[
                "DB_SERVER_RO_PORT"],
            database=str(self.entry.custom_properties["DB_NAME"])
        )

        self.engine = create_engine(
            self.DATABASE_URL,
            pool_size=20,
            max_overflow=10,
            pool_recycle=3600,
            pool_timeout=30,
            pool_pre_ping=True,
            echo=False,
            echo_pool=False
        ).execution_options(
            schema_translate_map={None: self.schema}
        )

    # ADD: Proper cleanup methods
    def close(self):
        """Synchronous cleanup method."""
        if self._closed:
            return
        self._closed = True
        try:
            """Explicitly close all connections and dispose engines."""
            if self.engine:
                self.engine.dispose()
                self.engine = None
            # Handle async engine disposal properly
            if self.engine_async:
                # Use run_until_complete for async engine disposal

                try:
                    loop = asyncio.get_event_loop()
                    # Schedule the disposal as a task
                    loop.create_task(self._async_dispose())

                    # if loop.is_running():
                    #     # If we're in an async context, schedule disposal
                    #     asyncio.create_task(self.engine_async.dispose())
                    # else:
                    #     loop.run_until_complete(self.engine_async.dispose())
                except RuntimeError:
                    # No running loop, create a new one for cleanup
                    try:
                        asyncio.run(self._async_dispose())
                    except Exception as e:
                        logger.warning(f"Error during async engine disposal: {e}")
                    # Create new event loop if none exists
                    # new_loop = asyncio.new_event_loop()
                    # new_loop.run_until_complete(self.engine_async.dispose())
                    # new_loop.close()
        except Exception as e:
            logger.warning(f"Error during PostgresSessionManager cleanup: {e}")

    async def _async_dispose(self):
        """Helper method for async engine disposal."""
        if self.engine_async:
            try:
                await self.engine_async.dispose()
                self.engine_async = None
            except asyncio.CancelledError:
                # Handle cancellation gracefully
                logger.info("Async engine disposal was cancelled")
                raise
            except Exception as e:
                logger.warning(f"Error disposing async engine: {e}")
                raise





    async def aclose(self):
        """Async cleanup method."""
        if self._closed:
            return

        self._closed = True
        try:
            if self.engine:
                self.engine.dispose()
                self.engine = None
            if self.engine_async:
                await self.engine_async.dispose()
                self.engine_async = None
        except asyncio.CancelledError:
            logger.info("PostgresSessionManager async cleanup was cancelled")
            raise
        except Exception as e:
            logger.warning(f"Error during PostgresSessionManager async cleanup: {e}")


    def __del__(self):
        """Ensure cleanup on garbage collection."""
        if not self._closed:
            try:
                # Only dispose sync engine in __del__ to avoid event loop issues
                if self.engine:
                    self.engine.dispose()
                # self.engine = None
            except Exception:
                pass

    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        """Context manager for sync sessions."""
        if self._closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        session_maker = sessionmaker(
            bind=self.engine,
            autocommit=False,
            autoflush=False,
        )
        session = session_maker()
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    @asynccontextmanager
    async def async_session(self) -> AsyncGenerator[Any, Any]:
        if self._closed:
            raise RuntimeError("PostgresSessionManager has been closed")
        async_session = sessionmaker(
            bind=self.engine_async,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False
        )
        async with async_session() as session:
            try:
                yield session
            except asyncio.CancelledError:
                await session.rollback()
                logger.info("Async session was cancelled, rolled back")
                raise
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    def update_schema(self, new_schema: str) -> Self:
        """Dynamically updates the schema and refreshes the engines."""
        if self._closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        self.schema = new_schema

        if self.engine_async:
            # Update schema_translate_map on existing engines
            self.engine_async = self.engine_async.execution_options(
                schema_translate_map={None: self.schema}
            )
        if self.engine:
            self.engine = self.engine.execution_options(
                schema_translate_map={None: self.schema}
            )

        return self

    def get_schemas(self) -> list[str]:
        """
        Retrieve all schemas from the regular (sync) engine.

        :return: List of schema names.
        """
        if self._closed:
            raise RuntimeError("PostgresSessionManager has been closed")
        inspector = inspect(self.engine, raiseerr=True)
        return inspector.get_schema_names()

    async def get_schemas_async(self) -> list[str]:
        """Retrieve all schemas from the async engine."""
        if self._closed:
            raise RuntimeError("PostgresSessionManager has been closed")

        async with self.engine_async.connect() as connection:
            return await connection.run_sync(
                lambda conn: inspect(conn).get_schema_names()
            )

    @classmethod
    async def cleanup_all_instances(cls):
        """Cleanup all active instances. Call this during application shutdown."""
        cleanup_tasks = []
        for instance in list(cls._instances):
            if not instance._closed:
                cleanup_tasks.append(instance.aclose())

        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as e:
                logger.warning(f"Error during cleanup of all instances: {e}")


class DatabaseLifecycleManager:
    """Manages the lifecycle of database connections for the application."""

    def __init__(self):
        self._session_managers = []
        self._cleanup_registered = False
        self._setup_cleanup_handlers()

    def register_session_manager(self, session_manager: PostgresSessionManager):
        """Register a session manager for cleanup."""
        self._session_managers.append(session_manager)

    def _setup_cleanup_handlers(self):
        """Setup cleanup handlers for different shutdown scenarios."""
        if self._cleanup_registered:
            return

        # Register atexit handler for normal program termination
        atexit.register(self._sync_cleanup)

        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)

        self._cleanup_registered = True

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, cleaning up database connections...")
        self._sync_cleanup()

    def _sync_cleanup(self):
        """Synchronous cleanup method."""
        for session_manager in self._session_managers:
            try:
                session_manager.close()
            except Exception as e:
                logger.warning(f"Error cleaning up session manager: {e}")

        self._session_managers.clear()

    async def async_cleanup(self):
        """Asynchronous cleanup method."""
        cleanup_tasks = []
        for session_manager in self._session_managers:
            if not session_manager._closed:
                cleanup_tasks.append(session_manager.aclose())

        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as e:
                logger.warning(f"Error during async cleanup: {e}")

        self._session_managers.clear()


# Global lifecycle manager
db_lifecycle_manager = DatabaseLifecycleManager()


class ManagedPostgresSessionManager(PostgresSessionManager):
    """PostgresSessionManager that auto-registers with lifecycle manager."""

    def __init__(self, entry: EntryDetails, schema: str, rw: bool = True):
        super().__init__(entry, schema, rw)
        # Auto-register with lifecycle manager
        db_lifecycle_manager.register_session_manager(self)

# Updated container with managed session managers
class DatabaseSessionManagerContainer(containers.DeclarativeContainer):
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    schema = providers.Object("public")
    wiring_config = containers.WiringConfiguration(
        modules=[
            "containers", "utility_code", "__main__", __name__,
        ]
    )

    pg_rw_entry = providers.Provider()
    pg_ro_entry = providers.Provider()

    # Use managed session managers that auto-register for cleanup
    database_rw = providers.Singleton(  # Changed to Singleton for better lifecycle management
        ManagedPostgresSessionManager,
        entry=pg_rw_entry.provided,
        schema=schema,
        rw=True
    )

    database_ro = providers.Singleton(  # Changed to Singleton for better lifecycle management
        ManagedPostgresSessionManager,
        entry=pg_ro_entry.provided,
        schema=schema,
        rw=False
    )

    # Lifecycle manager provider
    lifecycle_manager = providers.Object(db_lifecycle_manager)


# Context manager for proper async cleanup
@asynccontextmanager
async def database_lifecycle():
    """Context manager for managing database lifecycle in async applications."""
    try:
        yield
    finally:
        await db_lifecycle_manager.async_cleanup()


# Decorator for functions that need database cleanup
def with_database_cleanup(func):
    """Decorator that ensures database cleanup after function execution."""
    if asyncio.iscoroutinefunction(func):
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            finally:
                await db_lifecycle_manager.async_cleanup()
        return async_wrapper
    else:
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            finally:
                db_lifecycle_manager._sync_cleanup()
        return sync_wrapper



# Application shutdown handler
class ApplicationShutdownHandler:
    """Handles graceful shutdown of the application."""

    def __init__(self):
        self._shutdown_event = asyncio.Event()
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        import signal

        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())

        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

    async def shutdown(self):
        """Perform graceful shutdown."""
        if self._shutdown_event.is_set():
            return

        self._shutdown_event.set()
        logger.info("Starting graceful shutdown...")

        try:
            # Cleanup all PostgresSessionManager instances
            await PostgresSessionManager.cleanup_all_instances()
            logger.info("All database connections cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

        logger.info("Graceful shutdown completed")

    def is_shutting_down(self) -> bool:
        """Check if shutdown is in progress."""
        return self._shutdown_event.is_set()


# Global shutdown handler instance
shutdown_handler = ApplicationShutdownHandler()


def build_entry_details(keepass_manager: PyKeePass, title: str) -> EntryDetails:
    """Logic to retrieve and build EntryDetails, previously in get_kp_entry_details."""
    try:
        entry = keepass_manager.find_entries(title=title, first=True)
        if not entry:
            raise LookupError(f"No entry found with given title {title}")

        builder = EntryDetailsBuilder()
        builder.set_username(entry.username).set_password(entry.password).set_url(entry.url)
        for custom_property in entry.custom_properties:
            builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))

        return builder.build()

    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")


class FieldNameExtractor:
    def __init__(self, config):
        self.config = config

    def get_field_names(self):
        return [field_name["id"] for field_name in self.config["fields"]]

    def get_field_ids_by_datatype(self, datatype):
        """
        Given a datatype, returns the list of 'id' values for fields with the specified datatype.

        :param datatype: The datatype to filter by
        :return: List of 'id' values matching the datatype
        """
        return [field['id'] for field in self.config["fields"] if field['datatype'] == datatype]

# def instantiate_keepass() -> PyKeePass:
#     keepass_db = os.getenv("AIRFLOW_HOME") + "/Database.kdbx"
#     keepass_key = os.getenv("AIRFLOW_HOME") + "/Database.key"
#     if not keepass_db or not keepass_key:
#         raise ValueError("Environment variables DATABASE_PATH and MASTER_KEYFILE must be set.")
#
#     try:
#         ref = PyKeePass(keepass_db, keyfile=keepass_key)
#     except Exception as e:
#         raise ValueError(f"Failed to initialize Keepass reference: {e}")
#     return ref

class IssueFieldsContainer(containers.DeclarativeContainer):
    config_path = os.getenv("ISSUE_FIELDS_YAML_FILE", "issue_fields.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    field_name_extractor = providers.Factory(
        FieldNameExtractor,
        config=config,
    )
    wiring_config = containers.WiringConfiguration(
        modules=[
            "__main__", __name__, "dags.data_pipeline.utilities.profilers",
        ]
    )


class KeePassContainer(containers.DeclarativeContainer):
    # config = providers.Configuration(yaml_files=["./logging_config.yaml"])
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    rw_title = providers.Object('test_rw')
    ro_title = providers.Object('test_ro')

    # Use the extracted value in the callable function

    wiring_config = containers.WiringConfiguration(
        modules=[".containers", ".utility_code", "__main__"],
        from_package="dags.data_pipeline"
    )

    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE
    )

    # DON'T DELETE. KEEP IT FOR REFERENCE
    # keepass_manager = providers.Singleton(
    #     PyKeePass,
    #     filename=providers.Callable(lambda: os.getenv("AIRFLOW_HOME") + "/Database.kdbx"),
    #     keyfile=providers.Callable(lambda: os.getenv("AIRFLOW_HOME") + "/Database.key")
    # )
    # DON'T DELETE. KEEP IT FOR REFERENCE

    # Provider for `get_kp_entry_details`
    jira_entry_details = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.KeePass.JIRA_ENTRY,
    )

    pg_rw = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.KeePass.PG_RW,
    )

    pg_ro = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=config.KeePass.PG_RO,
    )

    schema_rw = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=rw_title,
    )

    schema_ro = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=ro_title,
    )


class JiraEntryDetailsContainer(containers.DeclarativeContainer):
    config = providers.Configuration(yaml_files=["./logging_config.yaml"])
    keepass = providers.Provider()

    jira_entry_details = providers.Singleton(
        build_entry_details,
        keepass_manager=keepass,
        title=config.KeePass.JIRA_ENTRY,
    )
    wiring_config = containers.WiringConfiguration(["__main__", "utility_code"])


class DatabaseSessionManagerContainer(containers.DeclarativeContainer):
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    config = providers.Configuration(yaml_files=[config_path])

    # config = providers.Configuration(yaml_files=["./logging_config.yaml"])
    schema = providers.Object("public")
    wiring_config = containers.WiringConfiguration(
        modules=[
            "containers", "utility_code", "__main__", __name__,
        ]
    )

    pg_rw_entry = providers.Provider()
    pg_ro_entry = providers.Provider()

    database_rw = providers.Factory(
        PostgresSessionManager,
        entry=pg_rw_entry.provided,
        schema=schema,
        rw=True
    )

    database_ro = providers.Factory(
        PostgresSessionManager,
        entry=pg_ro_entry.provided,
        schema=schema,
        rw=False
    )

    # Resource for cleanup on container shutdown
    @staticmethod
    def cleanup_database_connections():
        """Cleanup all database connections synchronously."""
        try:
            # Run cleanup in a new event loop if needed
            try:
                loop = asyncio.get_running_loop()
                # If we're in a running loop, we can't use asyncio.run()
                # Schedule the cleanup and let it run
                task = loop.create_task(PostgresSessionManager.cleanup_all_instances())
                # Don't await here as we're in a sync context
            except RuntimeError:
                # No running loop, safe to use asyncio.run()
                asyncio.run(PostgresSessionManager.cleanup_all_instances())
        except Exception as e:
            logger.warning(f"Error during database cleanup: {e}")

    # This won't automatically clean up, but provides a method to call
    cleanup_handler = providers.Callable(cleanup_database_connections)


class LoggerContainer(containers.DeclarativeContainer):
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    # config_file = providers.Configuration(yaml_files=["./logging_config.yaml"])
    config_file = providers.Configuration(yaml_files=[config_path])
    wiring_config = containers.WiringConfiguration(
        modules=[
            "__main__", __name__,
            # ".utilities.profilers",
        ]
    )
    log_resource = providers.Resource(
        logging.config.dictConfig,
        config=config_file.logging,
    )

    logger = providers.Singleton(logging.getLogger, name=config_file.Environment.Env)
    profiled_logger = providers.Singleton(logging.getLogger, name="profiled_logger")


class QueueContainer(containers.DeclarativeContainer):
    # config_path = os.getenv("LOGGING_CONFIG_PATH", "./logging_config.yaml")
    # config_file = providers.Configuration(yaml_files=["./logging_config.yaml"])
    # config = providers.Configuration(yaml_files=[config_path])
    # Load queue names and max depths from the YAML file
    config = providers.Configuration()

    # queue_issues = providers.Singleton(asyncio.Queue, maxsize=1000)
    # queue_stats = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_issue = providers.Singleton(asyncio.Queue, maxsize=100)
    # queue_changelog = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_worklog = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_comment = providers.Singleton(asyncio.Queue, maxsize=500)
    # queue_issue_links = providers.Singleton(asyncio.Queue, maxsize=500)

    queues = providers.Dict(
        queue_issues=providers.Factory(asyncio.Queue, maxsize=1000),
        queue_stats=providers.Factory(asyncio.Queue, maxsize=500),
        queue_issue=providers.Factory(asyncio.Queue, maxsize=100),
        queue_changelog=providers.Factory(asyncio.Queue, maxsize=500),
        queue_worklog=providers.Factory(asyncio.Queue, maxsize=500),
        queue_comment=providers.Factory(asyncio.Queue, maxsize=500),
        queue_issue_links=providers.Factory(asyncio.Queue, maxsize=500),
    )

    # base_queues = providers.Singleton(queues)

    # # Map schema names to shared queue instances
    # queue_schema = providers.Singleton(
    #     providers.Dict({
    #         "acq": base_queues,
    #         "plat": base_queues,
    #         "plp": base_queues,
    #         "train": base_queues,
    #     })
    # )

    # # Active queues for the current schema
    # active_queues = providers.Factory(
    #     lambda schema_name: QueueContainer.queue_schema()[schema_name]
    # )
    database_rw = providers.Dependency(instance_of=PostgresSessionManager)
    schemas = providers.Callable(
        lambda db_rw: db_rw.get_schemas(),
        db_rw=database_rw
    )

    queue_selector = providers.Selector(
        config.schema_name,
        acq=providers.Singleton(queues),
        plp=providers.Singleton(queues),
        plat=providers.Singleton(queues),
        train=providers.Singleton(queues),
        cpp=providers.Singleton(queues),
    )
    wiring_config = containers.WiringConfiguration(["__main__", "utility_code"])


class ApplicationContainer(containers.DeclarativeContainer):
    # Configuration
    config_path = os.getenv("LOGGING_CONFIG_PATH", "logging_config.yaml")
    # config_file = providers.Configuration(yaml_files=["./logging_config.yaml"])
    config = providers.Configuration(yaml_files=[config_path])
    # config = providers.Configuration(yaml_files=["./logging_config.yaml"])

    schema = providers.Object("public")  # Default schema
    # KeePass Manager
    keepass_manager = providers.Singleton(
        PyKeePass,
        filename=config.KeePassDir.DB_NAME,
        keyfile=config.KeePassDir.KEY_FILE
    )
    # Entry Providers
    pg_rw_entry = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=providers.Callable(lambda schema: f"{schema}_rw", schema=schema)
    )

    pg_ro_entry = providers.Factory(
        build_entry_details,
        keepass_manager=keepass_manager,
        title=providers.Callable(lambda schema: f"{schema}_ro", schema=schema)
    )

    # Database Session Managers
    database_rw = providers.Factory(
        PostgresSessionManager,
        entry=pg_rw_entry,
        schema=schema,
        rw=False
    )

    database_rw_managed = providers.Singleton(
        ManagedPostgresSessionManager,
        entry=pg_rw_entry,
        schema=schema,
        rw=False
    )

    database_ro = providers.Factory(
        PostgresSessionManager,
        entry=pg_ro_entry,
        schema=schema,
        rw=False
    )

    database_ro_managed = providers.Singleton(
        PostgresSessionManager,
        entry=pg_ro_entry,
        schema=schema,
        rw=False
    )

    # Lifecycle manager
    lifecycle_manager = providers.Object(db_lifecycle_manager)

    # Wiring
    wiring_config = containers.WiringConfiguration(
        modules=["utility_code", "__main__", __name__]
    )


@inject
def get_kp_entry_details(
        title: str | None = None,
        ref: PyKeePass = Provide[KeePassContainer.keepass_manager]
) -> EntryDetails:
    try:
        entry = ref.find_entries(title=title, first=True)
        if not entry:
            raise LookupError(f"No entry found with given title")

        builder = EntryDetailsBuilder()
        builder.set_username(entry.username).set_password(entry.password).set_url(entry.url)
        for custom_property in entry.custom_properties:
            builder.add_custom_property(custom_property, entry.get_custom_property(custom_property))

        return builder.build()

    except Exception as e:
        raise RuntimeError(f"Failed to find entry: {e}")


# class KeePassDetailsContainer(containers.DeclarativeContainer):
#     # Separate container to avoid circular import issues
#     entry_details = providers.Factory(get_kp_entry_details, "corecard Jira")
#     wiring_config = containers.WiringConfiguration(["__main__"])
#
#     encoded_token = providers.Factory(
#         lambda entry: base64.b64encode(f"{entry.username}:{entry.password}".encode()).decode(),
#         entry=entry_details
#     )
#     headers = providers.Callable(
#         lambda token: {
#             'Accept': "application/json",
#             'Content-Type': "application/json",
#             'Authorization': f'Basic {token}'
#         },
#         token=providers.Factory(
#             lambda entry: base64.b64encode(f"{entry.username}:{entry.password}".encode()).decode(),
#             entry=entry_details
#         )
#     )


async def fetch_data(url: str):
    async with aiohttp.ClientSession().get(url) as response:
        print(response.status)
        return await response.json()  # or `await response.json()` if expecting JSON


async def send_data(url: str, data: dict):
    async with aiohttp.ClientSession().put(url, json=data) as response:
        return await response.json()


async def main():
    url = "https://example.com/api"
    data = {"key": "value"}

    # Use `http_session_provider` as an async context manager
    async with aiohttp.ClientSession() as http_session:
        # fetched_data = await fetch_data(url=url, http_session=http_session)
        # print(f"Fetched Data: {fetched_data}")

        # response_data = await send_data(url=url, data=data, http_session=http_session)
        # print(f"Response Data: {response_data}")
        response_fields = await fetch_data(url="https://corecard.atlassian.net/rest/api/3/field")
        print(json.dumps(response_fields))


def prepare_issue_classification_data(project_key: str, pg_session) -> pd.DataFrame:
    topq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                            literal(1).label('level'),
                            case(

                                (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                (Issue.issuetype == 'Epic', 'epic'),
                                (Issue.isSubTask.is_(True), 'subtask'),

                                else_="standard"
                            ).label("issueclass"),
                            cast(cast(Issue.id, TEXT), LtreeType).label("path_id"),
                            cast(func.replace(Issue.key, "-", "_"), LtreeType).label("path_key")
                            )
    topq = topq.filter(Issue.parent_key.is_(None))
    topq = topq.cte('cte', recursive=True)

    bottomq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                               topq.c.level + 1,
                               case(
                                   (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                   (Issue.issuetype == 'Epic', 'epic'),
                                   (Issue.isSubTask.is_(True), 'subtask'),
                                   else_="standard"
                               ).label("issueclass"),
                               topq.c.path_id.op('||')(func.text2ltree(cast(Issue.id, TEXT))),
                               topq.c.path_key.op('||')(func.text2ltree(func.replace(Issue.key, "-", "_")))
                               )
    bottomq = bottomq.join(topq, Issue.parent_key == topq.c.key)
    recursive_q = topq.union_all(bottomq)
    res = pg_session.query(recursive_q).all()
    df = pd.DataFrame(res)
    # df = pd.read_sql(session.query(recursive_q).statement, session.bind)
    return df


# Usage example with proper cleanup
async def main_with_cleanup():
    """Example of proper usage with cleanup."""
    try:
        # Your application logic here
        keepass_container = KeePassContainer()
        await keepass_container.init_resources()

        database_container = DatabaseSessionManagerContainer()
        database_container.pg_rw_entry.override(keepass_container.pg_rw)
        database_container.pg_ro_entry.override(keepass_container.pg_ro)
        await database_container.init_resources()

        # Use the database instances
        db_rw = database_container.database_rw()

        async with db_rw.async_session() as session:
            # Your database operations
            pass

    except asyncio.CancelledError:
        logger.info("Main application was cancelled")
        raise
    except Exception as e:
        logger.error(f"Error in main application: {e}")
        raise
    finally:
        # Ensure cleanup happens
        await shutdown_handler.shutdown()


# Example 1: Sync usage with automatic cleanup
@inject
def sync_database_operation(
        db_rw: ManagedPostgresSessionManager = Provide[ApplicationContainer.database_rw_managed]
):
    """Example sync function with database operations."""
    # with database_container.database_rw().update_schema('plat').session() as pg_session:
    print(f"sync_database_operation called")
    with db_rw.session() as session:
        print(f"database url plat: {session.bind.engine}")
        df = prepare_issue_classification_data('plat', session)
        print(f"Total rows: {df.shape[0]}")
    # Cleanup will happen automatically via atexit/signal handlers


# Example 2: Async usage with context manager
async def async_database_operation():
    """Example async function with proper cleanup."""
    container = ApplicationContainer()
    container.wire(modules=[__name__])

    async with database_lifecycle():
        db_rw = container.database_rw()
        async with db_rw.async_session() as session:
            print(session.bind.engine)

        # Cleanup happens automatically when exiting context


# Example 3: Manual cleanup control
@inject
async def manual_cleanup_example(
        lifecycle_manager: DatabaseLifecycleManager = Provide[ApplicationContainer.lifecycle_manager]
):
    """Example with manual cleanup control."""
    try:
        # Your application logic
        pass
    finally:
        # Manual cleanup when needed
        await lifecycle_manager.async_cleanup()


# Example 4: Decorator-based cleanup
@with_database_cleanup
async def decorated_function():
    """Function with automatic cleanup via decorator."""
    container = ApplicationContainer()
    await container.init_resources()

    db_rw = container.database_rw()
    async with db_rw.async_session() as session:
        # Your database operations
        pass
    # Cleanup happens automatically via decorator

if __name__ == "__main__":
    # Example usage replacing your current main block
    # init_resources works with providers.Resource only

    container = ApplicationContainer()
    container.wire(modules=[__name__])


    async def async_main():
        """Async main function with proper cleanup."""
        async with database_lifecycle():
            # Initialize containers
            keepass_container = KeePassContainer()

            database_container = DatabaseSessionManagerContainer()
            database_container.pg_rw_entry.override(keepass_container.pg_rw)
            database_container.pg_ro_entry.override(keepass_container.pg_ro)
            database_container.wire(modules=[__name__])

            # Your existing operations
            db_rw = database_container.database_rw()

            async with db_rw.async_session() as session:
                # Your database operations
                pass

            # Test schema switching
            db_rw.update_schema('plat')
            async with db_rw.async_session() as session:
                # More operations
                pass


    def sync_main():
        """Sync main function - cleanup handled by atexit/signal handlers."""
        # Initialize containers
        keepass_container = KeePassContainer()
        # keepass_container.init_resources()

        database_container = DatabaseSessionManagerContainer()
        database_container.pg_rw_entry.override(keepass_container.pg_rw)
        database_container.pg_ro_entry.override(keepass_container.pg_ro)
        # database_container.init_resources()

        # Your existing operations
        db_rw = database_container.database_rw()
        sync_database_operation()

        with db_rw.session() as session:
            # Your database operations
            pass

        # Cleanup will happen automatically via registered handlers


    # Choose your approach:

    # Option 1: Async with explicit cleanup
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        print("Application interrupted")

    # Option 2: Sync with automatic cleanup (your current pattern)
    sync_main()

    # Enable asyncio debug mode
    try:
        asyncio.run(main_with_cleanup())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")

    keepass_container = KeePassContainer()
    keepass_container.init_resources()

    # Initialize DatabaseSessionManagerContainer with dependencies from KeePassContainer
    database_container = DatabaseSessionManagerContainer()
    database_container.pg_rw_entry.override(keepass_container.pg_rw)
    database_container.pg_ro_entry.override(keepass_container.pg_ro)
    # database_container.schema.override('plat')

    database_container.init_resources()
    database_container.wire(modules=[__name__])

    keepass_container.rw_title.override('acq_rw')
    print(keepass_container.schema_rw())
    database_container.pg_rw_entry.override(keepass_container.schema_rw)
    # Use the database instances
    db_rw = database_container.database_rw()
    db_ro = database_container.database_ro()

    with db_rw.session() as pg_session:
        print(pg_session.bind.engine)
        stmt = select(User.displayName)
        out = pg_session.execute(stmt).all()
        print(out)

    keepass_container.rw_title.override('plat_rw')
    database_container.pg_rw_entry.override(keepass_container.schema_rw)

    with database_container.database_rw().update_schema('plat').session() as pg_session:
        print(f"database url plat: {pg_session.bind.engine}")
        df = prepare_issue_classification_data('plat', pg_session)
        print(f"Total rows: {df.shape[0]}")

    # db_rw.update_schema('acq')
    # with db_rw.update_schema('acq').session() as pg_session:
    #     df = prepare_issue_classification_data('plat', pg_session)
    #     print(f"Total rows: {df.shape[0]}")

    with database_container.schema.override('plat'):
        print(database_container.schema())
        db_rw = database_container.database_rw()

        with db_rw.session() as pg_session:
            df = prepare_issue_classification_data('plat', pg_session)
            print(f"Total rows: {df.shape[0]}")

    # container.init_resources()
    cc_jira = get_kp_entry_details("corecard Jira")
    auth_token = f'{cc_jira.username}:{cc_jira.password}'
    print(f"from func call: {base64.b64encode(auth_token.encode()).decode()}")
